#pragma once

//
// RS485 Driver Interface Library
// High-level API for RS485 communication with AI-SLDAP devices
// Abstracts DeviceIoControl complexity and provides user-friendly interface
//

#include <windows.h>
#include <string>
#include <vector>
#include <functional>
#include <mutex>
#include <memory>

#include "../Include/RS485Common.h"
#include "../Include/RS485Protocol.h"
#include "../Include/RS485Errors.h"

//
// Forward Declarations
//
class RS485DriverInterface;

//
// Device Information Structure
//
struct DeviceInfo {
    std::string port;           // Device path for driver communication
    std::string description;    // Device description from Windows
    std::string serialNumber;   // Serial number (if available)
    std::string driverVersion;  // Driver version information
    bool isDriverLoaded;        // Whether the driver is currently loaded
    uint32_t vendorId;          // USB Vendor ID
    uint32_t productId;         // USB Product ID
};

//
// Port Information Structure
//
struct PortInfo {
    std::string devicePath;     // Full device path
    std::string friendlyName;   // User-friendly name
    bool isOpen;                // Port open status
    uint32_t baudRate;          // Current baud rate
    std::string driverVersion;  // Driver version
};

//
// Buffer Status Structure
//
struct BufferStatus {
    uint32_t uplinkUsed;        // Used payload slots in uplink buffer (0-5)
    uint32_t uplinkTotal;       // Total uplink buffer capacity (5 payload slots)
    uint32_t downlinkUsed;      // Used payload slots in downlink buffer (0-10)
    uint32_t downlinkTotal;     // Total downlink buffer capacity (10 payload slots)
    uint32_t payloadSize;       // Size per payload slot (12 bytes)
    bool isUplinkFull;          // Uplink buffer full flag
    bool isDownlinkFull;        // Downlink buffer full flag
    bool isOverflowDetected;    // Buffer overflow status
    uint32_t totalBufferBytes;  // Total buffer capacity in bytes (60 + 120 = 180 bytes)
};

//
// Hardware Status Structure
//
struct HardwareStatus {
    bool isConnected;           // Connection status
    uint32_t ftdiChipStatus;    // FTDI chip status flags
    uint32_t bufferOverflows;   // Number of buffer overflow events
    uint32_t crcErrors;         // Total CRC errors detected
    uint32_t timeoutErrors;     // Total timeout errors
    double signalStrength;      // RS485 signal strength (if available)
    uint32_t framesSent;        // Total frames transmitted
    uint32_t framesReceived;    // Total frames received successfully
};

//
// Performance Metrics Structure
//
struct PerformanceMetrics {
    double avgLatencyMs;        // Average response latency in milliseconds
    uint32_t bytesPerSecond;    // Throughput in bytes per second (payload data only)
    uint32_t successfulFrames;  // Number of successful frame transmissions
    uint32_t failedFrames;      // Number of failed frame transmissions
    uint32_t retryCount;        // Total number of retries performed
    double frameSuccessRate;    // Success rate as percentage (0-100)
    double avgBusUtilization;   // Average bus utilization percentage (0-100)
    uint32_t maxResponseTimeMs; // Maximum response time observed
    uint32_t crcErrorCount;     // Total CRC errors detected
    uint32_t timeoutCount;      // Total timeout errors encountered
    uint32_t bufferOverflowCount; // Number of buffer overflow events
};

//
// Line Status Structure
//
struct LineStatus {
    bool carrierDetect;         // CD - Carrier Detect signal status
    bool dataSetReady;          // DSR - Data Set Ready signal status
    bool ringIndicator;         // RI - Ring Indicator signal status
    bool clearToSend;           // CTS - Clear To Send signal status
    bool dataTerminalReady;     // DTR - Data Terminal Ready signal status
    bool requestToSend;         // RTS - Request To Send signal status
    uint32_t signalStrength;    // Signal strength indicator (0-100%)
    bool busIdle;               // True if RS485 bus is currently idle
    uint32_t noiseLevel;        // Background noise level (0-100%)
    bool hardwareError;         // Hardware-level error detected
    uint32_t lastActivityMs;    // Milliseconds since last bus activity
};

//
// Request Options Structure
//
struct RequestOptions {
    uint32_t timeoutMs;         // Request timeout in milliseconds
    uint32_t retryCount;        // Number of retry attempts
    bool waitForResponse;       // Whether to wait for response
};

//
// Callback Function Types
//
using ErrorCallbackFn = std::function<void(RS485_ERROR error, const char* message)>;
using ResponseCallbackFn = std::function<void(const uint8_t data[12])>;
using BufferThresholdCallbackFn = std::function<void(uint32_t currentUsage, uint32_t totalSize)>;

//
// Main RS485 Driver Interface Class
//
class RS485DriverInterface {
public:
    // Constructor and Destructor
    RS485DriverInterface();
    ~RS485DriverInterface();

    // Disable copy constructor and assignment operator
    RS485DriverInterface(const RS485DriverInterface&) = delete;
    RS485DriverInterface& operator=(const RS485DriverInterface&) = delete;

    // ===== ERROR HANDLE API =====
    // FTDI-style management functions
    RS485_CONNECTION_RESULT openPort(const std::string& devicePath);
    RS485_CONNECTION_RESULT closePort();
    bool isPortOpen() const;
    RS485_PORT_RESULT getPortInfo(PortInfo& info);

    // Device enumeration (similar to FTDI FT_ListDevices)
    static RS485_ENUMERATION_RESULT enumerateDevices(std::vector<DeviceInfo>& deviceList);
    static RS485_DETECTION_RESULT detectMultipleDevices(std::vector<uint8_t>& detectedAddresses);

    // Buffer management - CRITICAL for data integrity
    RS485_BUFFER_RESULT getBufferStatus(BufferStatus& status);
    RS485_BUFFER_RESULT checkUplinkBufferAvailability(bool& isFull);
    RS485_BUFFER_RESULT checkDownlinkBufferAvailability(bool& isFull);
    RS485_BUFFER_RESULT clearBuffer(RS485_BUFFER_TYPE bufferType = BufferTypeBoth);
    RS485_BUFFER_RESULT setBufferOverflowPolicy(RS485_BUFFER_OVERFLOW_POLICY policy);
    RS485_BUFFER_RESULT getBufferCapacity(uint32_t& uplinkFrames, uint32_t& downlinkFrames);

    // Hardware status (similar to FTDI FT_GetStatus)
    RS485_HARDWARE_RESULT getHardwareStatus(HardwareStatus& status);
    RS485_PERFORMANCE_RESULT getPerformanceMetrics(PerformanceMetrics& metrics);
    RS485_CONFIG_RESULT getBaudRate(uint32_t& currentBaudRate);
    RS485_LINE_RESULT getLineStatus(LineStatus& status);

    // Error handling and retry management
    const char* getErrorString(RS485_ERROR error) const;
    void registerErrorCallback(ErrorCallbackFn callback);
    void unregisterErrorCallback();

    // ===== MASTER BROADCASTING API =====
    // Automatic buffer flag checking before transmission
    // commandKey: 4-byte command identifier (e.g., "S001", "S002")
    // value: 8-byte payload data
    RS485_CONFIGURATION_RESULT configureSystemSettings(const std::string& commandKey, uint64_t value);
    RS485_VERIFICATION_RESULT verifySystemConfig(const std::string& commandKey, uint64_t expectedValue, bool& isMatching);

    // ===== MASTER ASSIGN DATA API =====
    // Automatic buffer flag checking before transmission
    // commandKey: 4-byte command identifier (e.g., "U001", "U002", etc.)
    // value: 8-byte payload data
    RS485_CONFIGURATION_RESULT configureUserSettings(const std::string& commandKey, uint64_t value);

    // Model data operations (W-series commands)
    // address: Memory address in FRAM
    // data: 12-byte payload data
    // isWrite: true for write operation, false for read
    // length: Data length (default 12 bytes)
    RS485_MODEL_DATA_RESULT modelDataOperation(uint32_t address, uint8_t data[12], bool isWrite, uint32_t length = 12);

    // ===== MASTER REQUEST API =====
    // Automatic buffer flag checking before transmission
    // Uses slave address previously set by S001 command
    // dataKey: 4-byte command identifier (e.g., "A001", "A002", etc.)
    RS485_REQUEST_RESULT requestData(const std::string& dataKey, const RequestOptions* options = nullptr);

    // ===== SLAVE RESPONSE API =====
    // Non-blocking design: PC requests data, then separately checks for response availability
    // Step 1: Check if slave has prepared response data (non-blocking)
    RS485_RESPONSE_RESULT checkSlaveDataReady(bool& isDataReady);

    // Step 2: Retrieve prepared response data (only when data is ready)
    // Uses fixed-size array for better performance and memory predictability
    // Note: Slave address is automatically managed by driver using S001 configuration
    RS485_RESPONSE_RESULT receiveSlaveResponse(uint8_t responseData[12], uint32_t timeout = 100);

    // Enhanced response handling with wait options
    RS485_RESPONSE_RESULT receiveSlaveResponse(uint8_t responseData[12], bool waitForData, uint32_t timeoutMs);

    // Response callback registration for asynchronous handling
    void registerResponseCallback(ResponseCallbackFn callback);
    void unregisterResponseCallback();

    // Buffer threshold monitoring
    RS485_BUFFER_RESULT setBufferThreshold(uint32_t thresholdPercent);
    void registerBufferThresholdCallback(BufferThresholdCallbackFn callback);

    // ===== UTILITY FUNCTIONS =====
    // Cross-platform data format helpers (users don't need to call these directly)
    static uint64_t encodeInteger(uint32_t value);
    static uint64_t encodeDualIntegers(uint32_t value1, uint32_t value2);
    static uint64_t encodeFloat(float value);
    static uint64_t encodeDouble(double value);

    static uint32_t decodeInteger(uint64_t payload);
    static std::pair<uint32_t, uint32_t> decodeDualIntegers(uint64_t payload);
    static float decodeFloat(uint64_t payload);
    static double decodeDouble(uint64_t payload);

    // Validation functions
    static bool validateIntegerRange(uint32_t value, uint32_t min, uint32_t max);
    static bool validateFloatRange(float value, float min, float max);

private:
    // Internal implementation details
    class Impl;
    std::unique_ptr<Impl> m_pImpl;

    // Thread safety
    mutable std::mutex m_apiMutex;

    // Current slave address for U-series commands
    uint8_t m_currentSlaveAddress;

    // Buffer management state
    RS485_BUFFER_OVERFLOW_POLICY m_bufferOverflowPolicy;
    uint32_t m_bufferThresholdPercent;

    // Callback functions
    ErrorCallbackFn m_errorCallback;
    ResponseCallbackFn m_responseCallback;
    BufferThresholdCallbackFn m_bufferThresholdCallback;

    // Internal helper methods
    RS485_IOCTL_RESULT sendIOCTL(DWORD ioctlCode, void* inputBuffer, DWORD inputSize,
                                void* outputBuffer, DWORD outputSize, DWORD* bytesReturned = nullptr);

    // Buffer flag checking - called before every transmission
    RS485_BUFFER_RESULT checkBufferBeforeTransmission();
    RS485_BUFFER_RESULT checkBufferBeforeStorage();

    // Error conversion helpers
    RS485_CONNECTION_RESULT mapWindowsErrorToConnectionResult(DWORD error);
    RS485_BUFFER_RESULT mapWindowsErrorToBufferResult(DWORD error);
    RS485_CONFIGURATION_RESULT mapWindowsErrorToConfigurationResult(DWORD error);
};

//
// Helper Classes for Type-Safe Operations
//

// Type-safe wrapper functions for common operations
class RS485DataHandler {
public:
    // Safe integer configuration with range validation
    static RS485_CONFIGURATION_RESULT configureIntegerSetting(
        RS485DriverInterface& driver,
        const std::string& key,
        uint32_t value,
        uint32_t minValue,
        uint32_t maxValue);

    // Safe float configuration with validation
    static RS485_CONFIGURATION_RESULT configureFloatSetting(
        RS485DriverInterface& driver,
        const std::string& key,
        float value,
        float minValue,
        float maxValue);

    // Safe dual integer configuration (for GPIO commands)
    static RS485_CONFIGURATION_RESULT configureDualIntegerSetting(
        RS485DriverInterface& driver,
        const std::string& key,
        uint32_t value1,
        uint32_t value2);
};

// Payload data extraction and storage helper
class PayloadDataExtractor {
public:
    // Extract key from bytes 0-3
    static std::string extractKey(const uint8_t* payload);

    // Extract 32-bit integer from bytes 4-7 (little-endian)
    static uint32_t extractInteger(const uint8_t* payload);

    // Extract IEEE 754 single-precision float from bytes 4-7
    static float extractFloat(const uint8_t* payload);

    // Extract IEEE 754 double-precision float from bytes 4-11
    static double extractDouble(const uint8_t* payload);

    // Extract dual 32-bit integers from bytes 4-7 and 8-11
    static std::pair<uint32_t, uint32_t> extractDualIntegers(const uint8_t* payload);

    // Store key into bytes 0-3 (ASCII string, null-padded)
    static void storeKey(uint8_t* payload, const std::string& key);

    // Store 32-bit integer into bytes 4-7 (little-endian, zero upper bytes)
    static void storeInteger(uint8_t* payload, uint32_t value);

    // Store IEEE 754 float into bytes 4-7 (zero upper bytes)
    static void storeFloat(uint8_t* payload, float value);

    // Store IEEE 754 double into bytes 4-11
    static void storeDouble(uint8_t* payload, double value);

    // Store dual 32-bit integers into bytes 4-7 and 8-11
    static void storeDualIntegers(uint8_t* payload, uint32_t value1, uint32_t value2);
};

//
// Simplified API Functions for Common Use Cases
//

// Simple configuration functions that handle data type conversion automatically
namespace RS485SimpleAPI {
    // Configure SEL threshold (250 mA, valid range 40-500)
    inline RS485_CONFIGURATION_RESULT configureSELThreshold(RS485DriverInterface& driver, uint32_t thresholdMA) {
        return RS485DataHandler::configureIntegerSetting(driver, "U001", thresholdMA, 40, 500);
    }

    // Configure maximum amplitude threshold (1500 mA, valid range 1000-2000)
    inline RS485_CONFIGURATION_RESULT configureMaxAmplitude(RS485DriverInterface& driver, uint32_t amplitudeMA) {
        return RS485DataHandler::configureIntegerSetting(driver, "U002", amplitudeMA, 1000, 2000);
    }

    // Configure power cycle duration (600 ms, valid values: 200,400,600,800,1000)
    inline RS485_CONFIGURATION_RESULT configurePowerCycleDuration(RS485DriverInterface& driver, uint32_t durationMs) {
        uint32_t validDurations[] = {200, 400, 600, 800, 1000};
        for (uint32_t validDuration : validDurations) {
            if (durationMs == validDuration) {
                return driver.configureUserSettings("U004", driver.encodeInteger(durationMs));
            }
        }
        return ConfigurationResultInvalidValue;
    }

    // Configure GPIO input channel (channel 0 or 1, enable true/false)
    inline RS485_CONFIGURATION_RESULT configureGPIOInput(RS485DriverInterface& driver, uint32_t channel, bool enable) {
        return RS485DataHandler::configureDualIntegerSetting(driver, "U005", channel, enable ? 1 : 0);
    }

    // Configure GPIO output channel (channel 0 or 1, enable true/false)
    inline RS485_CONFIGURATION_RESULT configureGPIOOutput(RS485DriverInterface& driver, uint32_t channel, bool enable) {
        return RS485DataHandler::configureDualIntegerSetting(driver, "U006", channel, enable ? 1 : 0);
    }

    // Set slave address (1-31)
    inline RS485_CONFIGURATION_RESULT setSlaveAddress(RS485DriverInterface& driver, uint32_t address) {
        return RS485DataHandler::configureIntegerSetting(driver, "S001", address, 1, 31);
    }

    // Set baud rate (9600, 19200, 38400, 57600, 115200)
    inline RS485_CONFIGURATION_RESULT setBaudRate(RS485DriverInterface& driver, uint32_t baudRate) {
        uint32_t validRates[] = {9600, 19200, 38400, 57600, 115200};
        for (uint32_t validRate : validRates) {
            if (baudRate == validRate) {
                return driver.configureSystemSettings("S002", driver.encodeInteger(baudRate));
            }
        }
        return ConfigurationResultInvalidValue;
    }
}

#endif // RS485_DRIVER_INTERFACE_H
