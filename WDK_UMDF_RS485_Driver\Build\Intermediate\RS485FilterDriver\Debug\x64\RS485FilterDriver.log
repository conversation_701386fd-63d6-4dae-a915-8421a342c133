﻿  RS485Buffer.cpp
D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\WDK_UMDF_RS485_Driver\Include\RS485Common.h(10,10): error C1083: 无法打开包括文件: “wdf.h”: No such file or directory
  (编译源文件“RS485Buffer.cpp”)
  
  RS485Device.cpp
D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\WDK_UMDF_RS485_Driver\Include\RS485Common.h(10,10): error C1083: 无法打开包括文件: “wdf.h”: No such file or directory
  (编译源文件“RS485Device.cpp”)
  
  RS485FilterDriver.cpp
D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\WDK_UMDF_RS485_Driver\Include\RS485Common.h(10,10): error C1083: 无法打开包括文件: “wdf.h”: No such file or directory
  (编译源文件“RS485FilterDriver.cpp”)
  
  RS485Protocol.cpp
D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\WDK_UMDF_RS485_Driver\Include\RS485Common.h(10,10): error C1083: 无法打开包括文件: “wdf.h”: No such file or directory
  (编译源文件“RS485Protocol.cpp”)
  
  RS485Queue.cpp
D:\wjw_new_file\Software_design\RS485_Driver\RS485_driver_development\WDK_UMDF_RS485_Driver\Include\RS485Common.h(10,10): error C1083: 无法打开包括文件: “wdf.h”: No such file or directory
  (编译源文件“RS485Queue.cpp”)
  
  正在生成代码...
