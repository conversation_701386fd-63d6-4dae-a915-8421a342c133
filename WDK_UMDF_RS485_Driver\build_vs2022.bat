@echo off
REM RS485 Driver Build Script for Visual Studio 2022
REM 修复了平台工具集问题，使用标准VS2022工具集

echo ========================================
echo RS485 Driver Build Script (VS2022)
echo ========================================

REM 设置Visual Studio 2022环境
set VS_PATH=C:\Program Files\Microsoft Visual Studio\2022
set MSBUILD_PATH=%VS_PATH%\Professional\MSBuild\Current\Bin\MSBuild.exe

REM 检查Professional版本，如果不存在则尝试Community版本
if not exist "%MSBUILD_PATH%" (
    set MSBUILD_PATH=%VS_PATH%\Community\MSBuild\Current\Bin\MSBuild.exe
)

REM 检查Community版本，如果不存在则尝试Enterprise版本
if not exist "%MSBUILD_PATH%" (
    set MSBUILD_PATH=%VS_PATH%\Enterprise\MSBuild\Current\Bin\MSBuild.exe
)

if not exist "%MSBUILD_PATH%" (
    echo ERROR: 无法找到Visual Studio 2022 MSBuild
    echo 请确保已安装Visual Studio 2022 (Community/Professional/Enterprise)
    pause
    exit /b 1
)

echo 找到MSBuild: %MSBUILD_PATH%

REM 设置构建参数
set SOLUTION_FILE=RS485Driver.sln
set CONFIGURATION=Debug
set PLATFORM=x64

echo 解决方案文件: %SOLUTION_FILE%
echo 配置: %CONFIGURATION%
echo 平台: %PLATFORM%

echo.
echo ========================================
echo 开始构建...
echo ========================================

REM 清理之前的构建
echo 清理之前的构建...
"%MSBUILD_PATH%" "%SOLUTION_FILE%" /t:Clean /p:Configuration=%CONFIGURATION% /p:Platform=%PLATFORM% /v:minimal

REM 构建解决方案
echo 构建解决方案...
"%MSBUILD_PATH%" "%SOLUTION_FILE%" /t:Build /p:Configuration=%CONFIGURATION% /p:Platform=%PLATFORM% /v:normal /m

if errorlevel 1 (
    echo.
    echo ========================================
    echo 构建失败！
    echo ========================================
    echo 请检查以下几点：
    echo 1. 确保已安装Visual Studio 2022
    echo 2. 确保已安装Windows 10/11 SDK
    echo 3. 检查WDK路径是否正确
    echo 4. 查看上面的错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建成功！
echo ========================================

REM 显示输出文件
set OUTPUT_DIR=Build\%CONFIGURATION%\%PLATFORM%
echo 输出目录: %OUTPUT_DIR%
echo.
echo 生成的文件：
if exist "%OUTPUT_DIR%\RS485FilterDriver.dll" echo   ✓ RS485FilterDriver.dll
if exist "%OUTPUT_DIR%\RS485FilterDriver.inf" echo   ✓ RS485FilterDriver.inf
if exist "%OUTPUT_DIR%\RS485DriverInterface.lib" echo   ✓ RS485DriverInterface.lib
if exist "%OUTPUT_DIR%\RS485Test.exe" echo   ✓ RS485Test.exe

echo.
echo ========================================
echo 下一步操作：
echo ========================================
echo 1. 启用测试签名（需要管理员权限）：
echo    bcdedit /set testsigning on
echo    然后重启电脑
echo.
echo 2. 安装驱动：
echo    右键点击 %OUTPUT_DIR%\RS485FilterDriver.inf
echo    选择"安装"
echo.
echo 3. 运行测试：
echo    %OUTPUT_DIR%\RS485Test.exe
echo.

pause
